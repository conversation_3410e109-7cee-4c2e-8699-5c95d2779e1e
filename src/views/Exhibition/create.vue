<template>
  <ContentWrap>
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="auto">
      <template v-for="fieldGroup in fieldGroups" :key="fieldGroup.id">
        <el-row :gutter="20">
          <el-col
            v-for="field in fieldGroup.fields"
            :key="field.id"
            :span="getFieldSpan(fieldGroup)"
            v-show="isFieldVisible(field)"
          >
            <el-form-item :label="field.name" :prop="field.code" :required="field.required">
              <SimpleFormField
                :field="field"
                v-model="formData[field.code]"
                :field-options="getFieldOptions(field)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row :gutter="20">
        <el-col :span="12" :offset="6">
          <el-form-item >
              <el-button v-show="editType === 'create'" type="primary" size="large" @click="handleSelectTag">{{ !labelType ? '下一步' : '选择标签' }}</el-button>
              <el-button v-show="editType === 'edit'" type="primary" size="large" @click="handleSelectTag">{{ !labelType ? '下一步' : '选择标签' }}</el-button>
              <el-button v-show="editType === 'create'" type="primary" size="large" @click="handleSubmit">{{ !labelType ? '添加' : '添加' }}</el-button>
              <el-button v-show="editType === 'edit'" type="primary" size="large" @click="handleSubmit">{{ !labelType ? '完成' : '添加' }}</el-button>
          </el-form-item>
        </el-col>
    </el-row>

    </el-form>
    <TagMultiForm ref="tagMultiFormRef" @ok="onTagOk" />
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({ name: 'ExhibitionCreate' })
import * as DictDataApi from '@/api/system/dict/dict.data'
import * as BusinessDataApi from '@/api/system/business-data'
import * as FieldConfApi from '@/api/system/data/field-conf'
import * as ViewFormConfApi from '@/api/system/data/view-form-conf'
import { getFieldSpan, filterAndMarkGroups } from '@/utils/formatter'
import TagMultiForm from './components/TagMultiForm.vue'
import { ViewFormType } from '@/config/constants/enums/business'
import { ExhibitionFieldConfig } from '@/config/business'
import { useExhibitionStore } from '@/store/modules/exhibition'
import { useFormProcessor } from '@/hooks/useFormProcessor'
import { useLabelConfig } from '@/hooks/useLabelConfig'



const route = useRoute()
const router = useRouter()
const exhibitionStore = useExhibitionStore()

// 使用表单处理 hook
const {
  formData,
  formRules,
  fieldOptionsMap,
  FIELD_TYPES,
  processFieldGroups,
  initializeFormData,
  generateFormValidationRules,
  processSubmitData
} = useFormProcessor()

// 使用标签配置 hook
const {
  labelType,
  labelConfigData: creatDialogFormRender,
  initializeLabelType
} = useLabelConfig()

const formRef = ref()
const fieldGroups = ref<any[]>([])
const editType = ref<('create' | 'edit')>('create') // 是否是编辑
const pageType = ref<ExhibitionFieldConfig['key'] | undefined>(undefined)
const tagMultiFormRef = ref() // 弹窗控制
const tagData = ref()

// 获取字段选项
const fetchFieldOptions = async (field: any) => {
  const { list } = await DictDataApi.getDictDataPage({
    pageNo: 1,
    pageSize: 10,
    dictType: field.fieldConfExtDOList[0].value
  })
  fieldOptionsMap.value.set(field.code, list)
  // 其他类型可按需扩展
}

// 判断字段是否应该显示 todo zhaokun 这里需要测试
const isFieldVisible = (field: any) => {
  const linkage = field.linkage
  if (!linkage || !linkage.enabled) {
    return true // 没有关联配置或未启用，默认显示
  }

  const targetFieldId = linkage.targetFieldId
  const targetFieldValue = linkage.targetFieldValue
  const condition = linkage.condition

  if (!targetFieldId) {
    return true
  }

  // 找到目标字段的值
  let targetValue = null
  for (const group of fieldGroups.value) {
    for (const f of group.fields) {
      if (f.id === targetFieldId) {
        targetValue = formData.value[f.code]
        break
      }
    }
    if (targetValue !== null) break
  }

  // 根据条件判断是否显示
  switch (condition) {
    case 'equals':
      return targetValue === targetFieldValue
    case 'notEquals':
      return targetValue !== targetFieldValue
    case 'contains':
    case 'notContains':
    case 'startsWith':
    case 'endsWith':
      return String(targetValue).includes(String(targetFieldValue))
    default:
      return true
  }
}

// ==================== 辅助函数 ====================





// 处理提交
const handleSubmit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const manageId = (route.meta.manageId as string) || '1945688148020826113'

      // 使用 hook 中的方法处理提交数据
      const businessJson = {
        ...processSubmitData(fieldGroups.value),
        children: tagData.value?.map(i => {
          return { ...processSubmitData(i) }
        })
      }

      try {
        if (route.query.editType && route.query.id) {
          await BusinessDataApi.updateBusinessData({
            businessJson,
            id: route.query.id as string,
            manageId
          })
          ElMessage.success('更新成功')
        } else {
          await BusinessDataApi.createBusinessData({
            businessJson,
            manageId
          })
          ElMessage.success('添加成功')
        }
      } catch (err) {
        console.error(err)
        ElMessage.error(route.query.editType ? '更新失败' : '添加失败')
      }
    } else {
      console.error('表单验证失败')
    }
  })
}

const handleSelectTag = () => {
  if (tagData.value && Object.keys(tagData.value)?.length) {
    tagMultiFormRef.value.setData(tagData.value)
  }
  tagMultiFormRef.value.open(pageType)
}

const onTagOk = (data: any) => {
  // 这里可以拿到弹窗返回的标签和表单数据
  console.log('标签选择确定', data)
  tagData.value = data

}

const init = async () => {
  const manageId = (route.meta.manageId as string)  || '1945688148020826113'
  const {
    type: routeType = undefined,        // 页面类型：人口、房屋、
    editType: routeEditType = 'create', // 编辑类型：创建、更新
    id: recordId
  } = route.query

  editType.value = routeEditType as ('create' | 'edit')
  pageType.value = routeType as (ExhibitionFieldConfig['key'] | undefined)
  const formType = routeEditType === 'create' ?  ViewFormType.CREATE : ViewFormType.EDIT // 创建还是编辑
  const flagKey = routeEditType === 'create' ? 'addFlag' : 'editFlag'

  // 提前获取 exhibition store 中的数据
  const storeFormData = exhibitionStore.getFormData
  const disabledKeys = Object.keys(storeFormData);
  // console.log('Exhibition Store 数据:', storeFormData)

  let businessData = {}

  const fieldConfigs = await FieldConfApi.getFieldConfigListByManageId({
    manageId
  })

  if (routeEditType === 'edit') {
    businessData = await BusinessDataApi.getBusinessData({
      id: recordId as string,
      manageId
    })
  }

  const filteredRes = fieldConfigs.filter((item: any) => item[flagKey])
  const formConfData = await ViewFormConfApi.getViewFormConf({
    manageId,
    formType
  })

  const rawData = JSON.parse(formConfData.formJson)
  const allowedIds = filteredRes.map((item: any) => item.id)
  const filteredData = filterAndMarkGroups(rawData, allowedIds)

  // 设置字段组数据和初始化表单
  if (filteredData && Array.isArray(filteredData)) {
    // 处理字段组配置（传入 fetchFieldOptions 函数）
    processFieldGroups(filteredData, disabledKeys, fetchFieldOptions)
    fieldGroups.value = filteredData

    // 初始化表单数据
    initializeFormData(filteredData, storeFormData, businessData)

    // 生成验证规则
    generateFormValidationRules(filteredData)
  }

  // 获取标签类型配置
  initializeLabelType(fieldConfigs)
}


const getFieldOptions = (field: any): any[] => {
  if (field.fieldType === FIELD_TYPES.RADIO || field.fieldType === FIELD_TYPES.CHECKBOX) {
    return fieldOptionsMap.value.get(field.code) || []
  }
  return []
}

onMounted(() => {
  init()
})
</script>