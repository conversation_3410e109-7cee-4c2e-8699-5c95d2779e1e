import { ref, computed } from 'vue'
import type { FieldGroup, FieldConfigWithVisibility } from '@/config/constants/types/field'

/**
 * 字段联动条件类型
 */
export type LinkageCondition =
  | 'equals'
  | 'notEquals'
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith'
  | 'isEmpty'
  | 'isNotEmpty'
  | 'greaterThan'
  | 'lessThan'

/**
 * 字段映射缓存接口
 */
interface FieldMapping {
  id: string
  code: string
  groupIndex: number
  fieldIndex: number
}

/**
 * 字段可见性管理 Hook
 * 提供高性能的字段联动和可见性控制
 */
export function useFieldVisibility() {
  // 字段映射缓存
  const fieldMappingCache = ref<Map<string, FieldMapping>>(new Map())

  /**
   * 构建字段映射缓存
   * 避免重复遍历，提高查找性能
   */
  const buildFieldMappingCache = (fieldGroups: FieldGroup[]): void => {
    const cache = new Map<string, FieldMapping>()

    fieldGroups.forEach((group, groupIndex) => {
      group.fields.forEach((field, fieldIndex) => {
        // 确保字段有有效的 id
        if (field.id) {
          cache.set(field.id, {
            id: field.id,
            code: field.code,
            groupIndex,
            fieldIndex
          })
        }
      })
    })

    fieldMappingCache.value = cache
  }

  /**
   * 获取目标字段的值
   * 使用缓存提高查找性能
   */
  const getTargetFieldValue = (
    targetFieldId: string,
    fieldGroups: FieldGroup[],
    formData: Record<string, any>
  ): any => {
    // 如果缓存为空或过期，重新构建
    if (fieldMappingCache.value.size === 0) {
      buildFieldMappingCache(fieldGroups)
    }

    const mapping = fieldMappingCache.value.get(targetFieldId)
    if (!mapping) {
      console.warn(`字段 ${targetFieldId} 未找到`)
      return undefined
    }

    return formData[mapping.code]
  }

  /**
   * 执行条件判断
   * 支持多种联动条件
   */
  const evaluateCondition = (
    condition: LinkageCondition,
    targetValue: any,
    expectedValue: any
  ): boolean => {
    // 处理空值情况
    if (condition === 'isEmpty') {
      return targetValue == null || targetValue === '' ||
             (Array.isArray(targetValue) && targetValue.length === 0)
    }

    if (condition === 'isNotEmpty') {
      return targetValue != null && targetValue !== '' &&
             (!Array.isArray(targetValue) || targetValue.length > 0)
    }

    // 如果目标值为空，其他条件都返回 false
    if (targetValue == null || targetValue === '') {
      return false
    }

    const targetStr = String(targetValue)
    const expectedStr = String(expectedValue)

    switch (condition) {
      case 'equals':
        return targetValue === expectedValue

      case 'notEquals':
        return targetValue !== expectedValue

      case 'contains':
        return targetStr.includes(expectedStr)

      case 'notContains':
        return !targetStr.includes(expectedStr)

      case 'startsWith':
        return targetStr.startsWith(expectedStr)

      case 'endsWith':
        return targetStr.endsWith(expectedStr)

      case 'greaterThan':
        const targetNum = Number(targetValue)
        const expectedNum = Number(expectedValue)
        return !isNaN(targetNum) && !isNaN(expectedNum) && targetNum > expectedNum

      case 'lessThan':
        const targetNum2 = Number(targetValue)
        const expectedNum2 = Number(expectedValue)
        return !isNaN(targetNum2) && !isNaN(expectedNum2) && targetNum2 < expectedNum2

      default:
        console.warn(`未知的联动条件: ${condition}`)
        return true
    }
  }

  /**
   * 判断字段是否应该显示
   * 优化后的高性能版本
   */
  const isFieldVisible = (
    field: FieldConfigWithVisibility,
    fieldGroups: FieldGroup[],
    formData: Record<string, any>
  ): boolean => {
    const linkage = field.linkage

    // 没有联动配置或未启用，默认显示
    if (!linkage?.enabled || !linkage.targetFieldId) {
      return true
    }

    const { targetFieldId, targetFieldValue, condition = 'equals' } = linkage

    try {
      // 获取目标字段的值
      const targetValue = getTargetFieldValue(targetFieldId, fieldGroups, formData)

      // 执行条件判断
      return evaluateCondition(condition as LinkageCondition, targetValue, targetFieldValue)
    } catch (error) {
      console.error('字段可见性判断出错:', error)
      return true // 出错时默认显示
    }
  }

  /**
   * 批量判断多个字段的可见性
   * 提高批量处理性能
   */
  const batchCheckVisibility = (
    fields: FieldConfigWithVisibility[],
    fieldGroups: FieldGroup[],
    formData: Record<string, any>
  ): Record<string, boolean> => {
    // 预先构建缓存
    buildFieldMappingCache(fieldGroups)

    const result: Record<string, boolean> = {}

    fields.forEach(field => {
      if (field.id) {
        result[field.id] = isFieldVisible(field, fieldGroups, formData)
      }
    })

    return result
  }

  /**
   * 清除字段映射缓存
   * 在字段配置变更时调用
   */
  const clearCache = (): void => {
    fieldMappingCache.value.clear()
  }

  /**
   * 获取字段的联动依赖关系
   * 用于分析字段间的依赖链
   */
  const getFieldDependencies = (
    field: FieldConfigWithVisibility
  ): string[] => {
    const dependencies: string[] = []

    if (field.linkage?.enabled && field.linkage.targetFieldId) {
      dependencies.push(field.linkage.targetFieldId)
    }

    return dependencies
  }

  /**
   * 检查是否存在循环依赖
   * 防止字段联动形成死循环
   */
  const checkCircularDependency = (
    fields: FieldConfigWithVisibility[]
  ): { hasCircular: boolean; circularFields: string[] } => {
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    const circularFields: string[] = []

    const dfs = (fieldId: string): boolean => {
      if (recursionStack.has(fieldId)) {
        circularFields.push(fieldId)
        return true
      }

      if (visited.has(fieldId)) {
        return false
      }

      visited.add(fieldId)
      recursionStack.add(fieldId)

      const field = fields.find(f => f.id === fieldId)
      if (field?.linkage?.enabled && field.linkage.targetFieldId) {
        if (dfs(field.linkage.targetFieldId)) {
          circularFields.push(fieldId)
          return true
        }
      }

      recursionStack.delete(fieldId)
      return false
    }

    fields.forEach(field => {
      if (field.id && !visited.has(field.id)) {
        dfs(field.id)
      }
    })

    return {
      hasCircular: circularFields.length > 0,
      circularFields: [...new Set(circularFields)]
    }
  }

  return {
    // 核心方法
    isFieldVisible,
    batchCheckVisibility,

    // 缓存管理
    buildFieldMappingCache,
    clearCache,

    // 工具方法
    getTargetFieldValue,
    evaluateCondition,
    getFieldDependencies,
    checkCircularDependency,

    // 响应式数据
    fieldMappingCache: computed(() => fieldMappingCache.value)
  }
}
