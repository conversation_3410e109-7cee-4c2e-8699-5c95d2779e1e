import { ref, computed, readonly } from 'vue'
import { exhibitionFieldConfig } from '@/config/business'
import type { ExhibitionFieldConfig } from '@/config/business'
import type { LabelFieldConfig } from '@/config/constants/types/field'

/**
 * 标签配置匹配结果接口
 */
export interface LabelConfigMatchResult {
  config: ExhibitionFieldConfig
  matchedFields: LabelFieldConfig[]
  matchedFieldCodes: string[]
  matchScore: number // 匹配度评分
}

/**
 * 标签配置选项接口
 */
export interface LabelConfigOption {
  label: string
  value: ExhibitionFieldConfig['key']
  fields: readonly string[]
  description?: string
}

/**
 * 标签配置处理 Hook
 * 提供高性能的标签类型识别、配置匹配等功能
 */
export const useLabelConfig = () => {
  const labelType = ref<ExhibitionFieldConfig['key'] | ''>('')
  const labelConfigData = ref<LabelFieldConfig[]>([])

  // 配置缓存
  const configCache = ref<Map<string, ExhibitionFieldConfig>>(new Map())
  const fieldCodeCache = ref<Map<string, string[]>>(new Map())

  /**
   * 构建配置缓存
   * 提高查找性能
   */
  const buildConfigCache = (): void => {
    if (configCache.value.size === 0) {
      const cache = new Map<string, ExhibitionFieldConfig>()
      const fieldCache = new Map<string, string[]>()

      exhibitionFieldConfig.forEach(config => {
        cache.set(config.key, config)
        fieldCache.set(config.key, [...config.fields])
      })

      configCache.value = cache
      fieldCodeCache.value = fieldCache
    }
  }

  /**
   * 计算字段匹配度
   * @param configFields 配置要求的字段
   * @param availableFields 可用的字段
   * @returns 匹配度评分 (0-1)
   */
  const calculateMatchScore = (
    configFields: readonly string[],
    availableFields: LabelFieldConfig[]
  ): number => {
    const availableCodes = new Set(availableFields.map(f => f.code))
    const matchedCount = configFields.filter(code => availableCodes.has(code)).length
    return matchedCount / configFields.length
  }

  /**
   * 初始化标签类型配置
   * 根据字段配置匹配对应的标签类型（优化版本）
   */
  const initializeLabelType = (fieldConfigs: LabelFieldConfig[]): ExhibitionFieldConfig | null => {
    if (!fieldConfigs?.length) {
      labelType.value = ''
      labelConfigData.value = []
      return null
    }

    buildConfigCache()

    let bestMatch: LabelConfigMatchResult | null = null
    let bestScore = 0

    // 使用缓存进行高效匹配
    for (const config of exhibitionFieldConfig) {
      const matchedFields = config.fields
        .map((fieldCode: string) => fieldConfigs.find(f => f.code === fieldCode))
        .filter((field): field is LabelFieldConfig => Boolean(field))

      if (matchedFields.length > 0) {
        const matchScore = calculateMatchScore(config.fields, fieldConfigs)

        if (matchScore > bestScore) {
          bestScore = matchScore
          bestMatch = {
            config,
            matchedFields,
            matchedFieldCodes: matchedFields.map(f => f.code),
            matchScore
          }
        }

        // 如果找到完全匹配，直接返回
        if (matchScore === 1) {
          break
        }
      }
    }

    if (bestMatch) {
      labelType.value = bestMatch.config.key
      labelConfigData.value = bestMatch.matchedFields
      return bestMatch.config
    }

    labelType.value = ''
    labelConfigData.value = []
    return null
  }

  /**
   * 根据字段代码获取标签配置（优化版本）
   * 支持精确匹配和部分匹配
   */
  const getLabelConfigByFields = (
    fieldCodes: string[],
    exactMatch: boolean = true
  ): ExhibitionFieldConfig | null => {
    if (!fieldCodes?.length) return null

    buildConfigCache()
    const fieldCodeSet = new Set(fieldCodes)

    for (const config of exhibitionFieldConfig) {
      if (exactMatch) {
        // 精确匹配：要求所有字段都存在
        const hasAllFields = config.fields.every(fieldCode =>
          fieldCodeSet.has(fieldCode)
        )
        if (hasAllFields) {
          return config
        }
      } else {
        // 部分匹配：至少有一个字段匹配
        const hasAnyField = config.fields.some((fieldCode: string) =>
          fieldCodeSet.has(fieldCode)
        )
        if (hasAnyField) {
          return config
        }
      }
    }
    return null
  }

  /**
   * 获取所有匹配的标签配置
   * 返回按匹配度排序的配置列表
   */
  const getAllMatchingConfigs = (fieldCodes: string[]): LabelConfigMatchResult[] => {
    if (!fieldCodes?.length) return []

    buildConfigCache()
    const results: LabelConfigMatchResult[] = []
    const fieldCodeSet = new Set(fieldCodes)

    for (const config of exhibitionFieldConfig) {
      const matchedFieldCodes = config.fields.filter(code => fieldCodeSet.has(code))

      if (matchedFieldCodes.length > 0) {
        const matchScore = matchedFieldCodes.length / config.fields.length
        results.push({
          config,
          matchedFields: [], // 这里可以根据需要填充
          matchedFieldCodes,
          matchScore
        })
      }
    }

    // 按匹配度降序排序
    return results.sort((a, b) => b.matchScore - a.matchScore)
  }

  /**
   * 获取标签类型的显示文本（优化版本）
   */
  const getLabelTypeText = (type?: ExhibitionFieldConfig['key'] | string): string => {
    const targetType = type || labelType.value
    if (!targetType) return '未知类型'

    buildConfigCache()
    const config = configCache.value.get(targetType)
    return config?.label || '未知类型'
  }

  /**
   * 检查是否为有效的标签类型（优化版本）
   */
  const isValidLabelType = (type: string): boolean => {
    if (!type) return false
    buildConfigCache()
    return configCache.value.has(type)
  }

  /**
   * 获取所有可用的标签类型选项（优化版本）
   */
  const getAllLabelTypeOptions = (): LabelConfigOption[] => {
    return exhibitionFieldConfig.map(config => ({
      label: config.label,
      value: config.key,
      fields: config.fields,
      description: `需要字段: ${config.fields.join(', ')}`
    }))
  }

  /**
   * 根据标签类型获取配置
   */
  const getConfigByType = (type: ExhibitionFieldConfig['key']): ExhibitionFieldConfig | null => {
    buildConfigCache()
    return configCache.value.get(type) || null
  }

  /**
   * 获取标签类型所需的字段代码
   */
  const getRequiredFieldCodes = (type: ExhibitionFieldConfig['key']): string[] => {
    buildConfigCache()
    return fieldCodeCache.value.get(type) || []
  }

  /**
   * 检查字段配置是否满足标签类型要求
   */
  const validateFieldsForType = (
    type: ExhibitionFieldConfig['key'],
    fieldCodes: string[]
  ): { isValid: boolean; missingFields: string[] } => {
    const requiredFields = getRequiredFieldCodes(type)
    const fieldCodeSet = new Set(fieldCodes)
    const missingFields = requiredFields.filter(code => !fieldCodeSet.has(code))

    return {
      isValid: missingFields.length === 0,
      missingFields
    }
  }

  /**
   * 重置标签配置
   */
  const resetLabelConfig = (): void => {
    labelType.value = ''
    labelConfigData.value = []
  }

  /**
   * 清除缓存
   */
  const clearCache = (): void => {
    configCache.value.clear()
    fieldCodeCache.value.clear()
  }

  // 计算属性
  const currentConfig = computed(() => {
    if (!labelType.value) return null
    return configCache.value.get(labelType.value) || null
  })

  const hasValidConfig = computed(() => {
    return Boolean(labelType.value && labelConfigData.value.length > 0)
  })

  const configSummary = computed(() => {
    if (!currentConfig.value) return null

    return {
      type: labelType.value,
      label: currentConfig.value.label,
      fieldCount: labelConfigData.value.length,
      requiredFields: currentConfig.value.fields,
      matchedFields: labelConfigData.value.map(f => f.code)
    }
  })

  return {
    // 响应式数据
    labelType: readonly(labelType),
    labelConfigData: readonly(labelConfigData),

    // 计算属性
    currentConfig,
    hasValidConfig,
    configSummary,

    // 核心方法
    initializeLabelType,
    getLabelConfigByFields,

    // 查询方法
    getLabelTypeText,
    isValidLabelType,
    getAllLabelTypeOptions,
    getConfigByType,
    getRequiredFieldCodes,

    // 工具方法
    getAllMatchingConfigs,
    validateFieldsForType,
    calculateMatchScore,

    // 管理方法
    resetLabelConfig,
    buildConfigCache,
    clearCache,

    // 常量
    exhibitionFieldConfig: readonly(exhibitionFieldConfig)
  }
}
